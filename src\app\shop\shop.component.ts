import { Component, OnInit } from '@angular/core';
import { ShopService } from './shop.service';
import { Iproduct } from '../shared/models/Product';
import { IPagination } from '../shared/models/Pagnation';

@Component({
  selector: 'app-shop',
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.scss'
})
export class ShopComponent implements OnInit {
  product: Iproduct[] = [];
  loading: boolean = true;
  error: string | null = null;

  constructor(private shopService: ShopService) { }

  ngOnInit(): void {
    this.getAllProduct();
  }

  getAllProduct() {
    this.loading = true;
    this.error = null;

    this.shopService.getProduct().subscribe({
      next: (value: IPagination) => {
        this.product = value.data || [];
        this.loading = false;
        console.log('Products loaded:', value.data);
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.error = 'Failed to load products. Please try again.';
        this.loading = false;
      }
    });
  }
}
