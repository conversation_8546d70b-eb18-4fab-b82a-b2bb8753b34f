import { Component, OnInit } from '@angular/core';
import { ShopService } from './shop.service';
import { Iproduct } from '../shared/models/Product';
import { IPagination } from '../shared/models/Pagnation';

@Component({
  selector: 'app-shop',
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.scss'
})
export class ShopComponent implements OnInit {
  constructor(private shopService: ShopService) { }

  ngOnInit(): void {
    this.getAllProduct();
  }
  product: Iproduct[] = [];
  getAllProduct(){
    this.shopService.getProduct().subscribe({
      next:((value:IPagination)=>{
        this.product = value.data;
        console.log(value.data);
      })
    })
  }

}
