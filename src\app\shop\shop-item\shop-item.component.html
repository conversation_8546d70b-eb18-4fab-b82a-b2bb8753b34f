
<div class="product-card" *ngIf="Product">
  <div class="card-image-container">
    <img
      [src]="Product.photos && Product.photos.length > 0 ? 'https://localhost:7124/' + Product.photos[0].imageName : 'assets/placeholder.jpg'"
      class="main-image"
      [alt]="Product.name"
      (error)="onImageError($event)">
    <button class="favorite-btn" aria-label="Add to favorites">
      <i class="material-icons">favorite_border</i>
    </button>
    <div class="image-overlay">
      <button class="quick-view-btn">
        <i class="material-icons">visibility</i> Quick View
      </button>
    </div>
  </div>

  <div class="card-content">
    <div class="thumbnails" *ngIf="Product.photos && Product.photos.length > 1">
      @for (item of Product.photos; track $index) {
        <img
          [src]="'https://localhost:7124/' + item.imageName"
          alt=""
          class="thumbnail-img"
          [class.active]="$index === 0"
          (error)="onImageError($event)">
      }
    </div>

    <h3 class="product-title">{{Product.name || 'Product Name'}}</h3>
    <p class="product-description">{{Product.description || 'No description available'}}</p>

    <div class="price-section">
      <span class="current-price">{{Product.newPrice | currency:'EGP':'symbol':'1.2-2'}}</span>
      <span class="old-price" *ngIf="Product.oldPrice && Product.oldPrice > Product.newPrice">
        {{Product.oldPrice | currency:'EGP':'symbol':'1.2-2'}}
      </span>
      <span class="discount-badge" *ngIf="Product.oldPrice && Product.oldPrice > Product.newPrice">
        {{((Product.oldPrice - Product.newPrice) / Product.oldPrice * 100) | number:'1.0-0'}}% OFF
      </span>
    </div>

    <div class="rating-section">
      <div class="stars">
        <i class="material-icons">star</i>
        <i class="material-icons">star</i>
        <i class="material-icons">star</i>
        <i class="material-icons">star</i>
        <i class="material-icons">star_border</i>
      </div>
      <span class="rating-text">(4.0)</span>
    </div>

    <div class="action-buttons">
      <button class="add-to-cart-btn">
        <i class="material-icons">shopping_cart</i>
        <span>Add to Cart</span>
      </button>
      <button class="details-btn">
        <i class="material-icons">info</i>
      </button>
    </div>
  </div>
</div>

<!-- Loading state -->
<div class="product-card-skeleton" *ngIf="!Product">
  <div class="skeleton-image"></div>
  <div class="skeleton-content">
    <div class="skeleton-line"></div>
    <div class="skeleton-line short"></div>
    <div class="skeleton-price"></div>
  </div>
</div>

