
        <div class="card h-100" *ngIf="Product">
          <img
            [src]="Product.photos && Product.photos.length > 0 ? 'https://localhost:7124/' + Product.photos[0].imageName : 'assets/no-image.jpg'"
            class="card-img-top"
            [alt]="Product.name"
            onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
          <div class="card-body">
            <div class="thumbnails mb-3" *ngIf="Product.photos && Product.photos.length > 1">
              @for (item of Product.photos; track $index) {
              <img
                [src]="'https://localhost:7124/' + item.imageName"
                alt=""
                class="thumbnail-img small-img"
                onerror="this.style.display='none'">
              }
            </div>
            <h5 class="card-title">{{Product.name || 'Product Name'}}</h5>
            <p class="card-text">{{Product.description || 'No description available'}}</p>
            <p class="price">
              <span class="old-price" *ngIf="Product.oldPrice && Product.oldPrice > Product.newPrice">
                {{Product.oldPrice | currency:'EGP':'symbol':'1.2-2'}}
              </span>
              {{Product.newPrice | currency:'EGP':'symbol':'1.2-2'}}
            </p>
            <button class="btn btn-outline-primary favorite-icon">
              <i class="fa fa-bookmark"></i>
            </button>
            <div class="d-flex justify-content-between align-items-start">
              <button class="btn btn-dark">
                <i class="fa fa-shopping-cart"></i> Add To Cart
              </button>
              <button class="btn btn-success">
                <i class="fa fa-info-circle"></i> Details
              </button>
            </div>
          </div>
        </div>

