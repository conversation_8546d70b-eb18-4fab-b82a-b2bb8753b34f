<div class="container mt-5">
   <div class="row">
    <h1 class="main-title">Shopping</h1>
    <section class="col-md-3">
      <div class="sidebar">
        <h4 class="section-title">Sorting</h4>
        <div class="custom-select-wrapper mb-4">
          <select name="" id="" class="form-select">
            <option value="Name">Name</option>
            <option value="PriceAsn">Price: Low to High</option>
            <option value="PriceDsn">Price: High to Low</option>
          </select>
        </div>
        <h4 class="selection-title">Categories</h4>
      <ul class="list-group custom-list-group">
        <li class="list-group-item custom-list-item">Laptop</li>
        <li class="list-group-item custom-list-item">Mobile</li>
        <li class="list-group-item custom-list-item">Clothes</li>
      </ul>
      </div>

    </section>
    <section class="col-md-9">
      <div class="d-flex justify-content-between align-items-center mb-4" >
        <div class="paging-container">
          <span class="text-dark">Showing 3 of 5</span>
        </div>
        <div class="search-container">
          <div class="input-group search-bar">
            <input type="text" class="form-control" placeholder="Search...">
            <input type="button" class="btn btn-danger" value="Search">
            <input type="button" class="btn btn-dark" value="Reset">
          </div>
        </div>
      </div>
      <!-- Loading State -->
      <div class="row g-4" *ngIf="loading">
        @for (item of [1,2,3,4,5,6]; track $index) {
          <div class="col-lg-4 col-md-6">
            <app-shop-item></app-shop-item>
          </div>
        }
      </div>

      <!-- Error State -->
      <div class="alert alert-danger text-center" *ngIf="error && !loading">
        <i class="material-icons">error</i>
        <h4>{{error}}</h4>
        <button class="btn btn-primary mt-2" (click)="getAllProduct()">
          <i class="material-icons">refresh</i> Try Again
        </button>
      </div>

      <!-- Products -->
      <div class="row g-4" *ngIf="!loading && !error">
        @if (product && product.length > 0) {
          @for (item of product; track item.id) {
            <div class="col-lg-4 col-md-6">
              <app-shop-item [Product]="item"></app-shop-item>
            </div>
          }
        } @else {
          <div class="col-12">
            <div class="text-center py-5">
              <i class="material-icons" style="font-size: 64px; color: #ccc;">inventory_2</i>
              <h3 class="mt-3 text-muted">No Products Found</h3>
              <p class="text-muted">There are no products available at the moment.</p>
            </div>
          </div>
        }
      </div>

    </section>

   </div>
</div>
