// تصميم عصري للكارد مع الحفاظ على البساطة
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .card-body {
    padding: 20px;

    .thumbnails {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding-bottom: 5px;

      .thumbnail-img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.3s ease;

        &:hover {
          border-color: #007bff;
          transform: scale(1.1);
        }
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .card-text {
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 15px;
    }

    .price {
      font-size: 18px;
      font-weight: 700;
      color: #27ae60;
      margin-bottom: 15px;

      .old-price {
        font-size: 14px;
        color: #95a5a6;
        text-decoration: line-through;
        margin-right: 10px;
      }
    }

    .rating {
      height: 20px;
      background: linear-gradient(90deg, #f39c12 0%, #f39c12 80%, #ddd 80%, #ddd 100%);
      border-radius: 10px;
      position: relative;

      &::after {
        content: "★★★★☆";
        position: absolute;
        top: -2px;
        left: 0;
        color: #f39c12;
        font-size: 14px;
      }
    }

    .favorite-icon {
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #007bff;
        color: white;
        transform: scale(1.1);
      }

      i {
        font-size: 18px;
      }
    }

    .btn {
      border-radius: 10px;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 10px 15px;

      &:hover {
        transform: translateY(-2px);
      }

      i {
        margin-right: 5px;
      }
    }

    .btn-dark {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
      }
    }

    .btn-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
      border: none;

      &:hover {
        box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .card {
    .card-img-top {
      height: 200px;
    }

    .card-body {
      padding: 15px;

      .card-title {
        font-size: 16px;
      }

      .card-text {
        font-size: 13px;
      }

      .price {
        font-size: 16px;
      }

      .btn {
        padding: 8px 12px;
        font-size: 13px;
      }

      .favorite-icon {
        width: 40px;
        height: 40px;
      }
    }
  }
}

@media (max-width: 480px) {
  .card {
    .card-img-top {
      height: 180px;
    }

    .card-body {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 35px;
          height: 35px;
        }
      }

      .card-title {
        font-size: 15px;
      }

      .card-text {
        font-size: 12px;
      }

      .price {
        font-size: 15px;

        .old-price {
          font-size: 12px;
        }
      }

      .btn {
        padding: 6px 10px;
        font-size: 12px;

        i {
          margin-right: 3px;
        }
      }

      .favorite-icon {
        width: 35px;
        height: 35px;

        i {
          font-size: 16px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .product-card {
    .card-content {
      padding: 16px;

      .product-title {
        font-size: 16px;
      }

      .product-description {
        font-size: 13px;
      }

      .price-section {
        .current-price {
          font-size: 18px;
        }

        .old-price {
          font-size: 14px;
        }
      }

      .action-buttons {
        .add-to-cart-btn {
          padding: 10px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }

        .details-btn {
          width: 44px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-card {
    .card-image-container {
      aspect-ratio: 1/1;
    }

    .card-content {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 32px;
          height: 32px;
        }
      }

      .product-title {
        font-size: 15px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .product-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .price-section {
        gap: 8px;

        .current-price {
          font-size: 16px;
        }

        .old-price {
          font-size: 13px;
        }

        .discount-badge {
          font-size: 10px;
          padding: 3px 6px;
        }
      }

      .action-buttons {
        gap: 8px;

        .add-to-cart-btn {
          padding: 8px 10px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }

        .details-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
