.product-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

    .image-overlay {
      opacity: 1;
      visibility: visible;
    }

    .favorite-btn {
      opacity: 1;
      transform: scale(1);
    }
  }

  .card-image-container {
    position: relative;
    overflow: hidden;
    aspect-ratio: 4/3;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    .main-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &:hover .main-image {
      transform: scale(1.05);
    }

    .favorite-btn {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      color: #ff6b6b;
      font-size: 16px;
      cursor: pointer;
      opacity: 0;
      transform: scale(0.8);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #ff6b6b;
        color: white;
        transform: scale(1.1);
      }
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;

      .quick-view-btn {
        background: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        font-weight: 600;
        color: #333;
        cursor: pointer;
        transition: all 0.3s ease;
        transform: translateY(20px);

        &:hover {
          background: #333;
          color: white;
          transform: translateY(0);
        }

        i {
          margin-right: 8px;
        }
      }
    }
  }

  .card-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .thumbnails {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      overflow-x: auto;
      padding-bottom: 4px;

      &::-webkit-scrollbar {
        height: 2px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: #ddd;
        border-radius: 2px;
      }

      .thumbnail-img {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        flex-shrink: 0;

        &.active,
        &:hover {
          border-color: #007bff;
          transform: scale(1.05);
        }
      }
    }

    .product-title {
      font-size: 18px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .product-description {
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 16px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      flex: 1;
    }

    .price-section {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      flex-wrap: wrap;

      .current-price {
        font-size: 20px;
        font-weight: 700;
        color: #27ae60;
      }

      .old-price {
        font-size: 16px;
        color: #95a5a6;
        text-decoration: line-through;
      }

      .discount-badge {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .rating-section {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 20px;

      .stars {
        display: flex;
        gap: 2px;

        i {
          color: #f39c12;
          font-size: 14px;
        }
      }

      .rating-text {
        color: #7f8c8d;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      margin-top: auto;

      .add-to-cart-btn {
        flex: 1;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 16px;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 14px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          font-size: 16px;
        }
      }

      .details-btn {
        width: 48px;
        height: 48px;
        background: #ecf0f1;
        border: none;
        border-radius: 12px;
        color: #34495e;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        &:hover {
          background: #34495e;
          color: white;
          transform: translateY(-2px);
        }

        i {
          font-size: 16px;
        }
      }
    }
  }
}

// Skeleton Loading
.product-card-skeleton {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  animation: pulse 1.5s ease-in-out infinite alternate;

  .skeleton-image {
    aspect-ratio: 4/3;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .skeleton-content {
    padding: 20px;
    flex: 1;

    .skeleton-line {
      height: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-bottom: 12px;

      &.short {
        width: 70%;
      }
    }

    .skeleton-price {
      height: 20px;
      width: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 4px;
      margin-top: 16px;
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .product-card {
    .card-content {
      padding: 16px;

      .product-title {
        font-size: 16px;
      }

      .product-description {
        font-size: 13px;
      }

      .price-section {
        .current-price {
          font-size: 18px;
        }

        .old-price {
          font-size: 14px;
        }
      }

      .action-buttons {
        .add-to-cart-btn {
          padding: 10px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }

        .details-btn {
          width: 44px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-card {
    .card-image-container {
      aspect-ratio: 1/1;
    }

    .card-content {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 32px;
          height: 32px;
        }
      }

      .product-title {
        font-size: 15px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .product-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .price-section {
        gap: 8px;

        .current-price {
          font-size: 16px;
        }

        .old-price {
          font-size: 13px;
        }

        .discount-badge {
          font-size: 10px;
          padding: 3px 6px;
        }
      }

      .action-buttons {
        gap: 8px;

        .add-to-cart-btn {
          padding: 8px 10px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }

        .details-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
