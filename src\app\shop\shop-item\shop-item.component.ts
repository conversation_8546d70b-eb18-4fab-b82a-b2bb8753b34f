import { Component, Input } from '@angular/core';
import { Iproduct } from '../../shared/models/Product';

@Component({
  selector: 'app-shop-item',
  templateUrl: './shop-item.component.html',
  styleUrl: './shop-item.component.scss'
})
export class ShopItemComponent {
  @Input() Product: Iproduct | undefined;

  onImageError(event: any) {
    // Set a placeholder image when the original image fails to load
    event.target.src = 'assets/placeholder.jpg';
  }
}
