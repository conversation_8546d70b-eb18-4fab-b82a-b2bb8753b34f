{"name": "e-commerce", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:E-Commerce": "node dist/e-commerce/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^18.1.0", "@angular/common": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/platform-browser": "^18.1.0", "@angular/platform-browser-dynamic": "^18.1.0", "@angular/platform-server": "^18.1.0", "@angular/router": "^18.1.0", "@angular/ssr": "^18.1.0", "@types/jquery": "^3.5.32", "bootstrap": "^5.3.7", "express": "^4.18.2", "font-awesome": "^4.7.0", "jquery": "^3.7.1", "material-icons": "^1.13.14", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.0", "@angular/cli": "^18.1.0", "@angular/compiler-cli": "^18.1.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}