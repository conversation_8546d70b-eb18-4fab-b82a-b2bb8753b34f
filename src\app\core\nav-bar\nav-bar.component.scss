.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  padding: 1rem 0 !important;
  transition: all 0.3s ease !important;
  font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif !important;
  border: none !important;

  &.bg-light {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .container {
    max-width: 1200px;
  }

  .navbar-brand {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;

    &:hover {
      transform: scale(1.05) !important;
      color: #ffd700 !important;
      text-decoration: none !important;
    }

    .Logo {
      border-radius: 50%;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: rotate(5deg) scale(1.1);
      }
    }

    i {
      font-style: normal !important;
      color: white !important;
      font-weight: 600 !important;
    }
  }

  .navbar-nav {
    .nav-item {
      margin: 0 0.25rem;

      .nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500 !important;
        padding: 0.75rem 1.25rem !important;
        border-radius: 25px !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;

        .material-icons {
          font-size: 1.1rem !important;
        }

        .material-icons {
          &.favorite {
            color: rgba(255, 255, 255, 0.9) !important;
            font-size: 1rem !important;
            transition: all 0.3s ease !important;
          }
        }

        &:hover {
          color: white !important;
          background: rgba(255, 255, 255, 0.1) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
          text-decoration: none !important;

          .material-icons.favorite {
            color: #ff4757 !important;
            transform: scale(1.2) !important;
            animation: heartbeat 1s infinite !important;
          }
        }

        &.active {
          background: rgba(255, 255, 255, 0.2) !important;
          color: #ffd700 !important;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
        }
      }
    }
  }

  .form-inline {
    .input-group {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
      border-radius: 25px !important;
      overflow: hidden !important;
      background: white !important;
      transition: all 0.3s ease !important;
      min-width: 300px !important;

      &:focus-within {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        transform: scale(1.02);
      }

      .form-control {
        border: none;
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
        background: transparent;
        color: #333;

        &:focus {
          box-shadow: none;
          outline: none;
          border: none;
        }

        &::placeholder {
          color: #999;
          font-style: italic;
        }
      }

      .input-group-append {
        .btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          border: none !important;
          color: white !important;
          padding: 0.875rem 1.25rem !important;
          transition: all 0.3s ease !important;

          &:hover {
            background: linear-gradient(45deg, #764ba2, #667eea) !important;
            transform: scale(1.05) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
          }

          .material-icons {
            font-size: 1.2rem;
          }
        }
      }
    }
  }

  .navbar-nav.ml-auto {
    .nav-item {
      margin: 0 0.5rem;

      .nav-link {
        color: white !important;
        padding: 0.5rem !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
        background: none !important;

        &:hover {
          color: #ffd700 !important;
          transform: translateY(-2px) scale(1.1) !important;
          text-decoration: none !important;
        }

        &.dropdown-toggle::after {
          display: none;
        }

        .material-icons {
          font-size: 1.5rem;
        }
      }
    }
  }

  .dropdown {
    .dropdown-menu {
      background: white !important;
      border: none !important;
      border-radius: 15px !important;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
      padding: 1rem 0 !important;
      margin-top: 0.5rem !important;
      min-width: 200px !important;

      .dropdown-item {
        padding: 0.75rem 1.5rem !important;
        color: #333 !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;

        .material-icons {
          font-size: 1.2rem !important;
          opacity: 0.7 !important;
          transition: all 0.3s ease !important;
          -webkit-transition: all 0.3s ease !important;
          -moz-transition: all 0.3s ease !important;
          -ms-transition: all 0.3s ease !important;
          -o-transition: all 0.3s ease !important;
}

        &:nth-child(1) .material-icons {
          color: #041d36 !important;
        }

        &:nth-child(2) .material-icons {
          color: #007bff !important;
        }

        &:nth-child(4) .material-icons {
          color: #ffc107 !important;
        }

        &:nth-child(5) .material-icons {
          color: #6f42c1 !important;
        }

        &:nth-child(7) .material-icons {
          color: #dc3545 !important;
        }

        &:hover {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          transform: translateX(5px) !important;
          text-decoration: none !important;
          border-radius: 8px !important;
          margin: 0 0.5rem !important;

          .material-icons {
            opacity: 1 !important;
            transform: scale(1.1) !important;
            color: white !important;
          }
        }

        &:nth-child(1):hover {
          background: linear-gradient(135deg, #460ccc, #9168d0) !important;
        }

        &:nth-child(2):hover {
          background: linear-gradient(135deg, #007bff, #0056b3) !important;
        }

        &:nth-child(4):hover {
          background: linear-gradient(135deg, #ffc107, #e0a800) !important;
        }

        &:nth-child(5):hover {
          background: linear-gradient(135deg, #6f42c1, #5a32a3) !important;
        }

        &:nth-child(7):hover {
          background: linear-gradient(135deg, #dc3545, #c82333) !important;
        }

        &:focus {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;

          .material-icons {
            opacity: 1 !important;
          }
        }
      }

      .dropdown-divider {
        margin: 0.5rem 1rem !important;
        border-color: rgba(102, 126, 234, 0.2) !important;
        border-width: 1px !important;
        opacity: 0.5 !important;
      }
    }
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@media (max-width: 991px) {
  .navbar {
    padding: 0.75rem 0;

    .navbar-collapse {
      background: rgba(255, 255, 255, 0.95);
      margin-top: 1rem;
      border-radius: 15px;
      padding: 1rem;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

      .navbar-nav .nav-link {
        color: #333 !important;
        padding: 1rem 1.25rem !important;

        &:hover {
          color: #667eea !important;
          background: rgba(102, 126, 234, 0.1);
        }
      }

      .form-inline {
        margin: 1rem 0;
        width: 100%;

        .input-group {
          width: 100%;
          min-width: auto;
        }
      }

      .navbar-nav.ml-auto {
        flex-direction: row;
        justify-content: center;
        margin-top: 1rem;

        .nav-link {
          color: #333 !important;
          margin: 0 0.5rem;

          &:hover {
            color: #667eea !important;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .navbar {
    padding: 0.5rem 0;

    .navbar-brand {
      font-size: 1.25rem;
      gap: 0.5rem;

      .Logo {
        width: 25px !important;
        height: 25px !important;
      }
    }

    .form-inline .input-group {
      min-width: 250px;

      .form-control {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
      }

      .btn {
        padding: 0.75rem 1rem;
      }
    }
  }
}
