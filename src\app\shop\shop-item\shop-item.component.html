
<div class="product-card">
  <div class="card-image-container">
    <img src="https://localhost:7124/{{Product.photos[0].imageName}}" class="main-image" alt="{{Product.name}}">
    <button class="favorite-btn" aria-label="Add to favorites">
      <i class="fa fa-heart"></i>
    </button>
    <div class="image-overlay">
      <button class="quick-view-btn">
        <i class="fa fa-eye"></i> Quick View
      </button>
    </div>
  </div>

  <div class="card-content">
    <div class="thumbnails">
      @for (item of Product.photos; track $index) {
        <img src="https://localhost:7124/{{item.imageName}}" alt="" class="thumbnail-img" [class.active]="$index === 0">
      }
    </div>

    <h3 class="product-title">{{Product.name}}</h3>
    <p class="product-description">{{Product.description}}</p>

    <div class="price-section">
      <span class="current-price">{{Product.newPrice|currency:'EGP'}}</span>
      <span class="old-price">{{Product.oldPrice|currency:'EGP'}}</span>
      <span class="discount-badge">
        {{((Product.oldPrice - Product.newPrice) / Product.oldPrice * 100) | number:'1.0-0'}}% OFF
      </span>
    </div>

    <div class="rating-section">
      <div class="stars">
        <i class="fa fa-star"></i>
        <i class="fa fa-star"></i>
        <i class="fa fa-star"></i>
        <i class="fa fa-star"></i>
        <i class="fa fa-star-o"></i>
      </div>
      <span class="rating-text">(4.0)</span>
    </div>

    <div class="action-buttons">
      <button class="add-to-cart-btn">
        <i class="fa fa-shopping-cart"></i>
        <span>Add to Cart</span>
      </button>
      <button class="details-btn">
        <i class="fa fa-info-circle"></i>
      </button>
    </div>
  </div>
</div>

