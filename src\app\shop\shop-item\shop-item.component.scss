// تصميم عصري وجذاب للكارد
.card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  background: #ffffff;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);

    .card-img-top {
      transform: scale(1.03);
    }

    .favorite-icon {
      background: #e74c3c;
      color: white;
      transform: scale(1.05);
    }
  }

  .card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .card-body {
    padding: 20px;

    .thumbnails {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 8px 0;
      margin-bottom: 15px;

      &::-webkit-scrollbar {
        height: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #f8f9fa;
        border-radius: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #6c757d;
        border-radius: 6px;
      }

      .thumbnail-img {
        width: 45px;
        height: 45px;
        border-radius: 8px;
        object-fit: cover;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.2s ease;

        &:hover {
          border-color: #007bff;
          transform: scale(1.05);
        }
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
      line-height: 1.4;
    }

    .card-text {
      color: #6c757d;
      font-size: 14px;
      line-height: 1.5;
      margin-bottom: 15px;
    }

    .price {
      font-size: 20px;
      font-weight: 700;
      color: #28a745;
      margin-bottom: 15px;

      .old-price {
        font-size: 16px;
        color: #6c757d;
        text-decoration: line-through;
        margin-right: 10px;
        font-weight: 500;
      }
    }

    .favorite-icon {
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      transition: all 0.2s ease;
      background: #f8f9fa;
      color: #6c757d;
      border: 1px solid #dee2e6;

      &:hover {
        background: #e74c3c;
        color: white;
        border-color: #e74c3c;
      }

      i {
        font-size: 16px;
      }
    }

    .btn {
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.2s ease;
      padding: 10px 16px;
      font-size: 14px;

      &:hover {
        transform: translateY(-1px);
      }

      i {
        margin-right: 6px;
        font-size: 14px;
      }
    }

    .btn-dark {
      background: #495057;
      border: none;

      &:hover {
        background: #343a40;
        box-shadow: 0 4px 12px rgba(73, 80, 87, 0.3);
      }
    }

    .btn-success {
      background: #28a745;
      border: none;

      &:hover {
        background: #218838;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
      }
    }

    .d-flex {
      gap: 12px;
    }
  }
}

// Responsive Design بسيط ومناسب
@media (max-width: 768px) {
  .card {
    .card-img-top {
      height: 220px;
    }

    .card-body {
      padding: 18px;

      .thumbnails {
        .thumbnail-img {
          width: 40px;
          height: 40px;
        }
      }

      .card-title {
        font-size: 17px;
      }

      .card-text {
        font-size: 13px;
      }

      .price {
        font-size: 18px;

        .old-price {
          font-size: 15px;
        }
      }

      .favorite-icon {
        width: 38px;
        height: 38px;

        i {
          font-size: 15px;
        }
      }

      .btn {
        padding: 9px 14px;
        font-size: 13px;

        i {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .card {
    .card-img-top {
      height: 200px;
    }

    .card-body {
      padding: 15px;

      .thumbnails {
        .thumbnail-img {
          width: 35px;
          height: 35px;
        }
      }

      .card-title {
        font-size: 16px;
      }

      .card-text {
        font-size: 12px;
      }

      .price {
        font-size: 17px;

        .old-price {
          font-size: 14px;
        }
      }

      .favorite-icon {
        width: 36px;
        height: 36px;

        i {
          font-size: 14px;
        }
      }

      .btn {
        padding: 8px 12px;
        font-size: 12px;

        i {
          font-size: 12px;
        }
      }

      .d-flex {
        gap: 10px;
        flex-direction: column;

        .btn {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .product-card {
    .card-content {
      padding: 16px;

      .product-title {
        font-size: 16px;
      }

      .product-description {
        font-size: 13px;
      }

      .price-section {
        .current-price {
          font-size: 18px;
        }

        .old-price {
          font-size: 14px;
        }
      }

      .action-buttons {
        .add-to-cart-btn {
          padding: 10px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }

        .details-btn {
          width: 44px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-card {
    .card-image-container {
      aspect-ratio: 1/1;
    }

    .card-content {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 32px;
          height: 32px;
        }
      }

      .product-title {
        font-size: 15px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .product-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .price-section {
        gap: 8px;

        .current-price {
          font-size: 16px;
        }

        .old-price {
          font-size: 13px;
        }

        .discount-badge {
          font-size: 10px;
          padding: 3px 6px;
        }
      }

      .action-buttons {
        gap: 8px;

        .add-to-cart-btn {
          padding: 8px 10px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }

        .details-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
