import { Component, OnInit } from '@angular/core';
import { ShopService } from './shop.service';
import { Iproduct } from '../shared/models/Product';
import { IPagination } from '../shared/models/Pagnation';

@Component({
  selector: 'app-shop',
  templateUrl: './shop.component.html',
  styleUrl: './shop.component.scss'
})
export class ShopComponent implements OnInit {
  constructor(private shopService: ShopService) { }

  ngOnInit(): void {
    this.getAllProduct();
  }
  product: Iproduct[] = [];

  getAllProduct(){
    this.shopService.getProduct().subscribe({
      next: (value: IPagination) => {
        this.product = value.data || [];
        console.log('Products loaded:', value.data);
        console.log('Total products:', this.product.length);

        // طباعة تفاصيل أول منتج للتأكد من البيانات
        if (this.product.length > 0) {
          console.log('First product:', this.product[0]);
        }
      },
      error: (error) => {
        console.error('Error loading products:', error);
        // في حالة الخطأ، سنضع بيانات تجريبية
        this.product = this.getDummyProducts();
      }
    });
  }

  // بيانات تجريبية للاختبار
  getDummyProducts(): Iproduct[] {
    return [
      {
        id: 1,
        name: 'Sample Product 1',
        description: 'This is a sample product description for testing purposes.',
        newPrice: 299.99,
        oldPrice: 399.99,
        categoryName: 'Electronics',
        photos: [
          { imageName: 'sample1.jpg', productId: 1 },
          { imageName: 'sample2.jpg', productId: 1 }
        ]
      },
      {
        id: 2,
        name: 'Sample Product 2',
        description: 'Another sample product with different specifications.',
        newPrice: 199.99,
        oldPrice: 249.99,
        categoryName: 'Fashion',
        photos: [
          { imageName: 'sample3.jpg', productId: 2 }
        ]
      },
      {
        id: 3,
        name: 'Sample Product 3',
        description: 'Third sample product for comprehensive testing.',
        newPrice: 149.99,
        oldPrice: 199.99,
        categoryName: 'Home',
        photos: [
          { imageName: 'sample4.jpg', productId: 3 },
          { imageName: 'sample5.jpg', productId: 3 },
          { imageName: 'sample6.jpg', productId: 3 }
        ]
      }
    ];
  }

}
