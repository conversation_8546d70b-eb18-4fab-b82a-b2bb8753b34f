<div class="container mt-5">
   <div class="row">
    <h1 class="main-title">Shopping</h1>
    <section class="col-md-3">
      <div class="sidebar">
        <h4 class="section-title">Sorting</h4>
        <div class="custom-select-wrapper mb-4">
          <select name="" id="" class="form-select">
            <option value="Name">Name</option>
            <option value="PriceAsn">Price: Low to High</option>
            <option value="PriceDsn">Price: High to Low</option>
          </select>
        </div>
        <h4 class="selection-title">Categories</h4>
      <ul class="list-group custom-list-group">
        <li class="list-group-item custom-list-item">Laptop</li>
        <li class="list-group-item custom-list-item">Mobile</li>
        <li class="list-group-item custom-list-item">Clothes</li>
      </ul>
      </div>

    </section>
    <section class="col-md-9">
      <div class="d-flex justify-content-between align-items-center mb-4" >
        <div class="paging-container">
          <span class="text-dark">Showing 3 of 5</span>
        </div>
        <div class="search-container">
          <div class="input-group search-bar">
            <input type="text" class="form-control" placeholder="Search...">
            <input type="button" class="btn btn-danger" value="Search">
            <input type="button" class="btn btn-dark" value="Reset">
          </div>
        </div>
      </div>
      <div class="row g-4">
        @for (item of product; track $index) {
        <ng-container>
          <app-shop-item class="product-item col-lg-4 col-md-6" [Product]="item"></app-shop-item>
        </ng-container>
      }
      </div>

    </section>

   </div>
</div>
