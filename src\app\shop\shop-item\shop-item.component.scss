// كود مختصر ومحافظ على الهوية البصرية
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  background: white;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

    .card-img-top { transform: scale(1.02); }
    .favorite-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
  }

  .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .card-body {
    padding: 18px;

    .thumbnails {
      display: flex;
      gap: 6px;
      overflow-x: auto;
      margin-bottom: 12px;

      &::-webkit-scrollbar { height: 2px; }
      &::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 2px;
      }

      .thumbnail-img {
        width: 35px;
        height: 35px;
        border-radius: 6px;
        object-fit: cover;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.2s ease;

        &:hover {
          border-color: #667eea;
          transform: scale(1.05);
        }
      }
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .card-text {
      color: rgba(255, 255, 255, 0.7);
      font-size: 13px;
      margin-bottom: 12px;
      color: #6c757d;
    }

    .price {
      font-size: 18px;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 12px;

      .old-price {
        font-size: 14px;
        color: #95a5a6;
        text-decoration: line-through;
        margin-right: 8px;
        font-weight: 500;
      }
    }

    .favorite-icon {
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      transition: all 0.3s ease;
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      border: none;

      i { font-size: 14px; }
    }

    .btn {
      border-radius: 25px;
      font-weight: 500;
      transition: all 0.3s ease;
      padding: 8px 16px;
      font-size: 13px;
      border: none;

      &:hover { transform: translateY(-2px); }

      i {
        margin-right: 5px;
        font-size: 12px;
      }
    }

    .btn-dark {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

      &:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }
    }

    .btn-success {
      background: rgba(255, 255, 255, 0.9);
      color: #667eea;
      border: 1px solid rgba(102, 126, 234, 0.3);

      &:hover {
        background: white;
        color: #5a67d8;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
      }
    }

    .d-flex { gap: 10px; }
  }
}

// Responsive مختصر
@media (max-width: 768px) {
  .card {
    .card-img-top { height: 180px; }
    .card-body {
      padding: 15px;
      .card-title { font-size: 15px; }
      .card-text { font-size: 12px; }
      .price { font-size: 16px; }
      .favorite-icon { width: 32px; height: 32px; }
      .btn { padding: 7px 12px; font-size: 12px; }
    }
  }
}

@media (max-width: 576px) {
  .card .card-body .d-flex {
    flex-direction: column;
    .btn { width: 100%; justify-content: center; }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .product-card {
    .card-content {
      padding: 16px;

      .product-title {
        font-size: 16px;
      }

      .product-description {
        font-size: 13px;
      }

      .price-section {
        .current-price {
          font-size: 18px;
        }

        .old-price {
          font-size: 14px;
        }
      }

      .action-buttons {
        .add-to-cart-btn {
          padding: 10px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }

        .details-btn {
          width: 44px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-card {
    .card-image-container {
      aspect-ratio: 1/1;
    }

    .card-content {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 32px;
          height: 32px;
        }
      }

      .product-title {
        font-size: 15px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .product-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .price-section {
        gap: 8px;

        .current-price {
          font-size: 16px;
        }

        .old-price {
          font-size: 13px;
        }

        .discount-badge {
          font-size: 10px;
          padding: 3px 6px;
        }
      }

      .action-buttons {
        gap: 8px;

        .add-to-cart-btn {
          padding: 8px 10px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }

        .details-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
