// تصميم عصري ومتطور للكارد
.card {
  border: none;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  position: relative;

  // تأثير الإضاءة
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);

    &::before {
      left: 100%;
    }

    .card-img-top {
      transform: scale(1.1);
    }

    .favorite-icon {
      transform: scale(1.2) rotate(10deg);
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
    }
  }

  .card-img-top {
    height: 280px;
    object-fit: cover;
    transition: transform 0.5s ease;
    position: relative;
    z-index: 0;
  }

  .card-body {
    padding: 25px;
    position: relative;
    z-index: 2;

    .thumbnails {
      display: flex;
      gap: 10px;
      overflow-x: auto;
      padding: 10px 0;
      margin-bottom: 15px;

      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(90deg, #667eea, #764ba2);
        border-radius: 10px;
      }

      .thumbnail-img {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        object-fit: cover;
        cursor: pointer;
        border: 3px solid transparent;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

        &:hover {
          border-color: #667eea;
          transform: scale(1.15) rotate(5deg);
          box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }
      }
    }

    .card-title {
      font-size: 20px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 12px;
      line-height: 1.3;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .card-text {
      color: #6c757d;
      font-size: 15px;
      line-height: 1.6;
      margin-bottom: 18px;
      font-weight: 400;
    }

    .price {
      font-size: 22px;
      font-weight: 800;
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 18px;
      text-shadow: 0 2px 4px rgba(39, 174, 96, 0.2);

      .old-price {
        font-size: 16px;
        color: #95a5a6;
        text-decoration: line-through;
        margin-right: 12px;
        font-weight: 500;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 2px;
          background: #e74c3c;
          transform: translateY(-50%);
        }
      }
    }

    .rating {
      height: 25px;
      background: linear-gradient(90deg, #f39c12 0%, #f39c12 80%, #ecf0f1 80%, #ecf0f1 100%);
      border-radius: 15px;
      position: relative;
      margin-bottom: 20px;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);

      &::after {
        content: "★★★★☆";
        position: absolute;
        top: 50%;
        left: 8px;
        transform: translateY(-50%);
        color: #f39c12;
        font-size: 16px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }

    .favorite-icon {
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
      }

      &:hover::before {
        width: 100px;
        height: 100px;
      }

      i {
        font-size: 20px;
        z-index: 1;
        position: relative;
      }
    }

    .btn {
      border-radius: 15px;
      font-weight: 600;
      transition: all 0.3s ease;
      padding: 12px 20px;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateY(-3px);

        &::before {
          left: 100%;
        }
      }

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .btn-dark {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);

      &:hover {
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.5);
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      }
    }

    .btn-success {
      background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
      border: none;
      box-shadow: 0 6px 20px rgba(86, 171, 47, 0.3);

      &:hover {
        box-shadow: 0 10px 30px rgba(86, 171, 47, 0.5);
        background: linear-gradient(135deg, #4a9b2e 0%, #96d9b8 100%);
      }
    }

    .d-flex {
      gap: 15px;
    }
  }
}

// Responsive Design محسن
@media (max-width: 992px) {
  .card {
    &:hover {
      transform: translateY(-5px) scale(1.01);
    }

    .card-img-top {
      height: 240px;
    }

    .card-body {
      padding: 20px;

      .card-title {
        font-size: 18px;
      }

      .price {
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .card {
    border-radius: 15px;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    .card-img-top {
      height: 220px;
    }

    .card-body {
      padding: 18px;

      .thumbnails {
        gap: 8px;

        .thumbnail-img {
          width: 45px;
          height: 45px;
        }
      }

      .card-title {
        font-size: 17px;
        margin-bottom: 10px;
      }

      .card-text {
        font-size: 14px;
        margin-bottom: 15px;
      }

      .price {
        font-size: 19px;
        margin-bottom: 15px;

        .old-price {
          font-size: 15px;
        }
      }

      .favorite-icon {
        width: 45px;
        height: 45px;
        margin-bottom: 18px;

        i {
          font-size: 18px;
        }
      }

      .btn {
        padding: 10px 16px;
        font-size: 13px;

        i {
          margin-right: 6px;
          font-size: 15px;
        }
      }

      .d-flex {
        gap: 12px;
      }
    }
  }
}

@media (max-width: 576px) {
  .card {
    border-radius: 12px;
    margin-bottom: 20px;

    &:hover {
      transform: translateY(-2px);
    }

    .card-img-top {
      height: 200px;
    }

    .card-body {
      padding: 15px;

      .thumbnails {
        gap: 6px;
        padding: 8px 0;

        .thumbnail-img {
          width: 40px;
          height: 40px;
          border-radius: 8px;
        }
      }

      .card-title {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .card-text {
        font-size: 13px;
        margin-bottom: 12px;
        line-height: 1.4;
      }

      .price {
        font-size: 18px;
        margin-bottom: 12px;

        .old-price {
          font-size: 14px;
          margin-right: 8px;
        }
      }

      .rating {
        height: 22px;
        margin-bottom: 15px;

        &::after {
          font-size: 14px;
          left: 6px;
        }
      }

      .favorite-icon {
        width: 42px;
        height: 42px;
        margin-bottom: 15px;

        i {
          font-size: 17px;
        }
      }

      .btn {
        padding: 9px 14px;
        font-size: 12px;
        border-radius: 12px;

        i {
          margin-right: 5px;
          font-size: 14px;
        }
      }

      .d-flex {
        gap: 10px;
        flex-direction: column;

        .btn {
          width: 100%;
          justify-content: center;
        }

        .btn-success {
          order: -1;
        }
      }
    }
  }
}

@media (max-width: 400px) {
  .card {
    .card-img-top {
      height: 180px;
    }

    .card-body {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 35px;
          height: 35px;
        }
      }

      .card-title {
        font-size: 15px;
      }

      .card-text {
        font-size: 12px;
      }

      .price {
        font-size: 16px;

        .old-price {
          font-size: 13px;
        }
      }

      .favorite-icon {
        width: 38px;
        height: 38px;

        i {
          font-size: 16px;
        }
      }

      .btn {
        padding: 8px 12px;
        font-size: 11px;

        i {
          font-size: 13px;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .product-card {
    .card-content {
      padding: 16px;

      .product-title {
        font-size: 16px;
      }

      .product-description {
        font-size: 13px;
      }

      .price-section {
        .current-price {
          font-size: 18px;
        }

        .old-price {
          font-size: 14px;
        }
      }

      .action-buttons {
        .add-to-cart-btn {
          padding: 10px 12px;
          font-size: 13px;

          span {
            display: none;
          }
        }

        .details-btn {
          width: 44px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .product-card {
    .card-image-container {
      aspect-ratio: 1/1;
    }

    .card-content {
      padding: 12px;

      .thumbnails {
        .thumbnail-img {
          width: 32px;
          height: 32px;
        }
      }

      .product-title {
        font-size: 15px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .product-description {
        font-size: 12px;
        -webkit-line-clamp: 1;
        line-clamp: 1;
      }

      .price-section {
        gap: 8px;

        .current-price {
          font-size: 16px;
        }

        .old-price {
          font-size: 13px;
        }

        .discount-badge {
          font-size: 10px;
          padding: 3px 6px;
        }
      }

      .action-buttons {
        gap: 8px;

        .add-to-cart-btn {
          padding: 8px 10px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }

        .details-btn {
          width: 40px;
          height: 40px;

          i {
            font-size: 14px;
          }
        }
      }
    }
  }
}
